/* Reset and base styles */
* {
  box-sizing: border-box;
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(191, 161, 79, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(191, 161, 79, 0.6), 0 0 30px rgba(191, 161, 79, 0.4);
  }
}

.tracker-body{
  background: linear-gradient(135deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  background-attachment: fixed;
  min-height: 100vh;
  padding: 10px;
  box-sizing: border-box;
  width: 100%;
  margin: 0;
  overflow-x: hidden;
  position: relative;
}

.tracker-body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 80%, rgba(191, 161, 79, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(191, 161, 79, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.tracker-container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0 10px;
  animation: fadeInUp 1s ease-out;
  position: relative;
  z-index: 1;
}

.tracker-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
  width: 100%;
}

.tracker-grid > *:first-child {
  animation: slideInLeft 1.2s ease-out;
}

.tracker-grid > *:last-child {
  animation: slideInRight 1.2s ease-out;
}

.table-wrapper {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0 10px;
  overflow-x: hidden;
}

.summary-container {
  background: linear-gradient(135deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  border-radius: 12px;
  padding: 20px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6),
              inset 0 1px 0 rgba(191, 161, 79, 0.2);
  border: 1px solid rgba(191, 161, 79, 0.3);
}

.summary-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(191, 161, 79, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.summary-container:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.8),
              inset 0 1px 0 rgba(191, 161, 79, 0.4),
              0 0 20px rgba(191, 161, 79, 0.2);
}

.tracker-title {
  text-align: center;
  font-size: 2.2rem;
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  margin-bottom: 25px;
  color: #bfa14f;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  animation: fadeInUp 1.5s ease-out;
  transition: all 0.3s ease;
  letter-spacing: 1px;
  position: relative;
}

.tracker-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #bfa14f, transparent);
}

.tracker-title:hover {
  color: #d4af37;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.9);
  transform: scale(1.02);
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.summary-grid .box:nth-child(1) { animation-delay: 0.2s; }
.summary-grid .box:nth-child(2) { animation-delay: 0.4s; }
.summary-grid .box:nth-child(3) { animation-delay: 0.6s; }
.summary-grid .box:nth-child(4) { animation-delay: 0.8s; }

.box {
  background: linear-gradient(135deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  border: 1px solid rgba(191, 161, 79, 0.4);
  border-radius: 15px;
  padding: 15px;
  text-align: center;
  font-weight: 600;
  color: #bfa14f;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeInUp 1s ease-out both;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5),
              inset 0 1px 0 rgba(191, 161, 79, 0.1);
  backdrop-filter: blur(10px);
}

.box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(191, 161, 79, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.box:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.7),
              inset 0 1px 0 rgba(191, 161, 79, 0.3),
              0 0 15px rgba(191, 161, 79, 0.2);
  border-color: rgba(191, 161, 79, 0.6);
}



.box:hover .amount {
  color: #808080; /* gray amount on hover */
  text-shadow: 0 0 10px rgba(128, 128, 128, 0.5);
}

.big-box {
  background: linear-gradient(135deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  border: 1px solid rgba(191, 161, 79, 0.4);
  border-radius: 20px;
  padding: 25px;
  text-align: center;
  color: #bfa14f;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeInUp 1.2s ease-out both;
  cursor: pointer;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.6),
              inset 0 1px 0 rgba(191, 161, 79, 0.15);
  backdrop-filter: blur(10px);
}

.big-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(191, 161, 79, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.big-box:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.8),
              inset 0 1px 0 rgba(191, 161, 79, 0.3),
              0 0 20px rgba(191, 161, 79, 0.25);
  border-color: rgba(191, 161, 79, 0.6);
}



.big-box:hover .amount {
  color: #808080; /* gray amount on hover */
  text-shadow: 0 0 15px rgba(128, 128, 128, 0.6);
  transform: scale(1.1);
}

.box-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.amount {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
  color: #bfa14f;
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.amount.counting {
  animation: countPulse 0.1s ease-in-out;
}

@keyframes countPulse {
  0% {
    transform: scale(1);
    color: #bfa14f;
  }
  50% {
    transform: scale(1.08);
    color: #d4af37;
    text-shadow: 0 0 15px rgba(191, 161, 79, 0.8);
  }
  100% {
    transform: scale(1);
    color: #bfa14f;
  }
}

/* Staggered animation delays for boxes */
.summary-box-0 { animation-delay: 0s; }
.summary-box-1 { animation-delay: 0.2s; }
.summary-box-2 { animation-delay: 0.4s; }
.summary-box-3 { animation-delay: 0.6s; }
.summary-box-4 { animation-delay: 0.8s; }

/* Enhanced counting effect */
.amount.counting {
  animation: countPulse 0.15s ease-in-out;
  position: relative;
}

.amount.counting::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(191, 161, 79, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  animation: ripple 0.15s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.table-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}


.table-scroll-container {
  overflow-x: auto;
  background: linear-gradient(60deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  border-radius: 12px;
  padding: 20px;
  -webkit-overflow-scrolling: touch;
  position: relative;
  animation: fadeInUp 1.4s ease-out;
  transition: all 0.4s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6),
              inset 0 1px 0 rgba(191, 161, 79, 0.2);
  border: 1px solid rgba(191, 161, 79, 0.3);
  backdrop-filter: blur(10px);
  width: 100%;
  max-width: 100%;
}

.table-scroll-container:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.8),
              inset 0 1px 0 rgba(191, 161, 79, 0.4),
              0 0 25px rgba(191, 161, 79, 0.15);
}

.table-scroll-container table {
  width: 100%;
  min-width: 600px;
  border-collapse: collapse;
  background: linear-gradient(135deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.table-scroll-container table:hover {
  transform: scale(1.001);
}

.table-scroll-container th,
.table-scroll-container td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #000; /* black borders */
  white-space: nowrap;
  color: #bfa14f; /* gold text */
  transition: all 0.3s ease;
}

.table-scroll-container tr:hover td {
  background-color: rgba(0, 0, 0, 0.3);
  color: #808080; /* gray on hover */
  transform: scale(1.01);
}

.table-scroll-container th {
  background: linear-gradient(135deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  color: #bfa14f; /* gold header text */
  font-weight: bold;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
  position: relative;
}

.table-scroll-container th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #000, transparent); /* black shimmer */
  animation: shimmer 2s infinite;
}

.financial-table {
  background-color: transparent;
  border-radius: 12px;
  padding: 16px;
  overflow-x: auto;
}

.refresh-btn, .action-btn {
  padding: 10px 20px;
  border: 1px solid rgba(191, 161, 79, 0.4);
  background: linear-gradient(135deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 4px;
  color: #bfa14f;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4),
              inset 0 1px 0 rgba(191, 161, 79, 0.1);
  backdrop-filter: blur(10px);
}

.refresh-btn {
  margin-top: 15px;
  background: linear-gradient(50deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  font-size: 14px;
  letter-spacing: 0.5px;
}

.action-btn {
  padding: 8px 16px;
  font-size: 12px;
  border-color: rgba(191, 161, 79, 0.4);
}

.refresh-btn:hover, .action-btn:hover {
  background: linear-gradient(135deg, #bfa14f 0%, #d4af37 30%, #000 70%, #808080 100%);
  transform: translateY(-2px);
  color: #000;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.6),
              inset 0 1px 0 rgba(191, 161, 79, 0.3),
              0 0 15px rgba(191, 161, 79, 0.2);
  border-color: rgba(191, 161, 79, 0.6);
}

.refresh-btn:hover {
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Override any Tailwind classes that might interfere */
.table-auto {
  table-layout: auto !important;
}

.w-full {
  width: 100% !important;
}

.border-collapse {
  border-collapse: collapse !important;
}

.text-center {
  text-align: center !important;
}

.mt-4 {
  margin-top: 1rem !important;
}

.add-record-section {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.add-record-btn {
  padding: 12px 24px;
  border: 1px solid rgba(191, 161, 79, 0.5);
  background: linear-gradient(135deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #bfa14f;
  font-size: 14px;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5),
              inset 0 1px 0 rgba(191, 161, 79, 0.2);
  backdrop-filter: blur(10px);
}

.add-record-btn:hover {
  background: linear-gradient(135deg, #bfa14f 0%, #d4af37 30%, #000 70%, #808080 100%);
  transform: translateY(-3px);
  color: #000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.7),
              inset 0 1px 0 rgba(191, 161, 79, 0.4),
              0 0 20px rgba(191, 161, 79, 0.3);
  border-color: rgba(191, 161, 79, 0.8);
}

/* Tablet Layout */
@media screen and (max-width: 768px) {
  .tracker-body { padding: 8px; }
  .tracker-grid { grid-template-columns: 1fr; gap: 15px; }
  .tracker-container, .table-wrapper { padding: 0 8px; width: 100%; }
}

/* Mobile Layout - Improved Responsive Design */
@media screen and (max-width: 600px) {
  .tracker-body {
    padding: 5px;
    width: 100vw;
    margin: 0;
    overflow-x: hidden;
  }

  .tracker-container {
    padding: 0 5px;
    width: 100%;
    max-width: 100%;
  }

  .table-wrapper {
    padding: 0 5px;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
  }

  /* Stack layout vertically on mobile */
  .tracker-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 20px;
    width: 100%;
  }

  .summary-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    width: 100%;
  }

  .summary-container {
    padding: 12px;
    width: 100%;
    margin: 0;
    order: 1;
  }

  .big-box {
    order: 2;
    padding: 15px;
    width: 100%;
    box-sizing: border-box;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tracker-title {
    font-size: 1.4rem;
    margin-bottom: 15px;
    font-weight: 700;
    text-align: center;
  }

  .amount {
    font-size: 20px !important;
    font-weight: bold;
  }

  .box {
    padding: 10px;
    font-size: 12px;
    width: 100%;
    box-sizing: border-box;
    min-height: 65px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .big-box .box-content {
    width: 100%;
  }

  .big-box .box-content > div:first-child {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .big-box .box-content > div:last-child {
    font-size: 24px !important;
    font-weight: bold;
    margin-top: 8px !important;
  }

  .table-scroll-container {
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table-scroll-container table {
    min-width: 500px;
    font-size: 12px;
    width: 100%;
  }

  .table-scroll-container th,
  .table-scroll-container td {
    padding: 8px 4px;
    font-size: 11px;
    line-height: 1.2;
    white-space: nowrap;
  }

  .table-scroll-container th {
    font-size: 12px;
    font-weight: bold;
  }

  .add-record-section {
    justify-content: center;
    margin-top: 15px;
    margin-bottom: 10px;
    width: 100%;
  }

  .add-record-btn {
    padding: 10px 20px;
    font-size: 13px;
    font-weight: 600;
    min-height: 40px;
  }

  .refresh-btn {
    padding: 10px 20px;
    font-size: 13px;
    font-weight: 600;
    min-height: 40px;
  }

  .action-btn {
    padding: 6px 10px;
    font-size: 10px;
    min-height: 32px;
    margin: 0 1px;
  }
}

/* Extra small screens - Optimized for very small devices */
@media screen and (max-width: 480px) {
  .tracker-body {
    padding: 3px;
    width: 100vw;
    overflow-x: hidden;
  }

  .tracker-container {
    padding: 0 3px;
  }

  .table-wrapper {
    padding: 0 3px;
    overflow-x: hidden;
  }

  /* Single column layout for very small screens */
  .tracker-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
    margin-bottom: 15px;
    width: 100%;
  }

  .summary-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px;
    width: 100%;
  }

  .summary-container {
    padding: 10px;
    order: 1;
  }

  .big-box {
    order: 2;
    padding: 12px;
    min-height: 70px;
  }

  .tracker-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 12px;
    text-align: center;
  }

  .box {
    padding: 8px;
    font-size: 11px;
    min-height: 55px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .big-box .box-content > div:first-child {
    font-size: 12px;
    margin-bottom: 6px;
  }

  .big-box .box-content > div:last-child {
    font-size: 20px !important;
    font-weight: bold;
  }

  .amount {
    font-size: 16px !important;
    font-weight: bold;
  }

  .table-scroll-container {
    padding: 8px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table-scroll-container table {
    min-width: 450px;
    font-size: 10px;
    width: 100%;
  }

  .table-scroll-container th,
  .table-scroll-container td {
    padding: 6px 3px;
    font-size: 9px;
    line-height: 1.1;
    white-space: nowrap;
  }

  .table-scroll-container th {
    font-size: 10px;
  }

  .add-record-section {
    justify-content: center;
    margin-top: 12px;
    margin-bottom: 8px;
  }

  .add-record-btn {
    padding: 8px 16px;
    font-size: 11px;
    min-height: 36px;
  }

  .refresh-btn {
    padding: 8px 16px;
    font-size: 11px;
    min-height: 36px;
  }

  .action-btn {
    padding: 4px 8px;
    font-size: 9px;
    min-height: 28px;
    margin: 0 1px;
  }
}
