/* Add Record Dialog - Luxury black/gold/gray theme aligned with Tracker UI */
:host {
  display: block;
  color: #bfa14f; /* gold text */
}

/* Style the Material dialog container itself */
:host ::ng-deep .mat-mdc-dialog-surface {
  background: linear-gradient(135deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  border: 1px solid rgba(191, 161, 79, 0.4);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6), inset 0 1px 0 rgba(191, 161, 79, 0.2);
  padding: 0; /* we will pad sections individually */
  color: #bfa14f;
}

/* Title */
[mat-dialog-title] {
  display: block;
  margin: 0;
  padding: 16px 24px;
  text-align: center;
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  font-size: 20px;
  letter-spacing: 1px;
  color: #bfa14f;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  border-bottom: 1px solid rgba(191, 161, 79, 0.25);
  background: linear-gradient(135deg, transparent, rgba(191, 161, 79, 0.05));
  position: relative;
}

[mat-dialog-title]::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #bfa14f, transparent);
}

/* Content area (use subtle gray for content zone as per preference) */
mat-dialog-content {
  padding: 16px 24px 8px;
  background: rgba(128, 128, 128, 0.08); /* subtle gray */
}

/* Form fields */
mat-form-field {
  width: 100%;
  margin-bottom: 12px;
}

:host ::ng-deep .mat-mdc-form-field-appearance-outline .mdc-notched-outline__leading,
:host ::ng-deep .mat-mdc-form-field-appearance-outline .mdc-notched-outline__notch,
:host ::ng-deep .mat-mdc-form-field-appearance-outline .mdc-notched-outline__trailing {
  border-color: rgba(191, 161, 79, 0.4);
}

:host ::ng-deep .mdc-text-field--focused .mdc-notched-outline__leading,
:host ::ng-deep .mdc-text-field--focused .mdc-notched-outline__notch,
:host ::ng-deep .mdc-text-field--focused .mdc-notched-outline__trailing {
  border-color: #d4af37; /* brighter gold on focus */
}

:host ::ng-deep .mat-mdc-form-field-label {
  color: rgba(191, 161, 79, 0.85);
}

:host ::ng-deep .mdc-text-field--focused .mat-mdc-form-field-label {
  color: #d4af37 !important;
}

:host ::ng-deep .mat-mdc-input-element {
  color: #bfa14f;
  caret-color: #d4af37;
}

:host ::ng-deep .mat-mdc-form-field-focus-overlay {
  background: rgba(191, 161, 79, 0.08);
}

/* Actions */
mat-dialog-actions {
  padding: 12px 16px 16px;
  border-top: 1px solid rgba(191, 161, 79, 0.25);
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(26, 26, 26, 0.6));
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* Buttons - inherit tracker style */
button[mat-button] {
  padding: 8px 14px;
  border: 1px solid rgba(191, 161, 79, 0.4);
  background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #bfa14f;
  font-weight: 500;
  text-transform: none;
}

button[mat-button]:hover {
  background: linear-gradient(135deg, #bfa14f 0%, #000 100%);
  transform: translateY(-1px);
  color: #000;
  border-color: rgba(191, 161, 79, 0.8);
}

button[mat-raised-button] {
  padding: 8px 16px;
  border: 1px solid rgba(191, 161, 79, 0.5);
  background: linear-gradient(135deg, #000 0%, #1a1a1a 60%, #bfa14f 90%, #808080 100%);
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #bfa14f;
  text-transform: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(191, 161, 79, 0.2);
}

button[mat-raised-button]:hover {
  background: linear-gradient(135deg, #bfa14f 0%, #d4af37 30%, #000 70%, #808080 100%);
  transform: translateY(-2px);
  color: #000;
  border-color: rgba(191, 161, 79, 0.8);
}

/* Responsive adjustments while keeping layout consistent */
@media screen and (max-width: 600px) {
  mat-dialog-title {
    font-size: 18px;
    padding: 12px 16px;
  }
  mat-dialog-content {
    padding: 12px 16px 8px;
  }
  mat-dialog-actions {
    padding: 10px 12px 12px;
  }
}